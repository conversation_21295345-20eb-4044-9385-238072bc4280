% ===================================================================
% 实验设置与分析 (Experimental Setup and Analysis)
% ===================================================================

\section{实验设置与分析}

% ===================================================================
% 实验设置 (Experimental Setup)
% ===================================================================


\begin{frame}{实验设置}
	% 上半部分: 关键问题
	\textbf{关键问题}
	\begin{itemize}
		\item \textbf{RQ1: }AgentVerse框架在通用理解与推理任务(如对话回复、创造性写作、数学推理、逻辑推理)中的性能是否优于是否优于单智能体?
		\item \textbf{RQ2: }AgentVerse框架在编码任务(如Humaneval代码补全)中的性能是否优于是否优于单智能体方法?
		\item \textbf{RQ3: }AgentVerse框架在需要多种工具交互的复杂任务中，性能是否优于独立的ReAct智能体?
		\item \textbf{RQ4: }在具身场景(如Minecraft)下，AgentVerse框架下的多智能体协作是否会涌现特定社会行为?
	\end{itemize}

	\vspace{0.1em} % 增加垂直间距以分隔两部分

	% 下半部分: 实验设置
	\textbf{实验设置}
	\begin{itemize}
		\item \textbf{语言模型}: GPT-3.5-Turbo-0613、GPT-4-0613、GPT-4-0314(用于 Minecraft)
		\item \textbf{集成工具}: Bing Search API、Web Browser、Code Interpreter、Weather API、Billboard API
		\item \textbf{数据集}: 通用理解能力 (FED、Commongen-Challenge)、通用推理能力 (MGSM、Logic Grid Puzzles)、代码能力 (Humaneval )、工具使用 (10个自定义复杂指令)
		\item \textbf{Baseline}: CoT、Solo、ReAct Agent
	\end{itemize}
\end{frame}


\begin{frame}{RQ1: AgentVerse框架在通用理解与推理任务中的性能是否优于单智能体?}
	\begin{center}
		\includegraphics[width=0.9\linewidth]{pic/RQ1.png}
	\end{center}

	{\scriptsize
		\begin{itemize}
			\item CoT: 带CoT（思维链）的单智能体；
			\item Solo:  在决策阶段使用一个智能体的AgentVerse。与CoT相比，Solo额外结合了专家招募、动作执行和评估模块；
			\item Group: 在决策过程中使用多个智能体协作实现AgentVerse。
		\end{itemize}
	}
	AgentVerse框架在通用理解与推理任务中的性能优于单智能体方法。

\end{frame}

\begin{frame}{RQ2: AgentVerse框架在编码任务中的性能是否优于单智能体方法?}

	\vspace{0.4em}
	% 上栏：任务描述
	以 \textbf{HumanEval 代码补全任务}（核心指标为\texttt{pass@1}，即代码首次生成即正确的通过率）为基准，对比CoT、Solo、Group三种设置的编码能力。

	\vspace{0.4em}

	% 中栏：图片和数据对比
	\begin{columns}
		% 中左：图片
		\begin{column}{0.45\textwidth}
			\begin{center}
				\includegraphics[width=\linewidth]{pic/RQ2.png}
			\end{center}
		\end{column}

		% 中右：数据对比
		\begin{column}{0.5\textwidth}
			{
				\begin{itemize}
					\item GPT-4：从 CoT 的 83.5 $\rightarrow$ Solo 的 87.2 $\rightarrow$ Group 的 89.0，\textcolor{red}{\textbf{Group 比 CoT 提升 5.5 个百分点}}；
					\item GPT-3.5-Turbo：从 CoT 的 73.8 $\rightarrow$ Solo 的 74.4 $\rightarrow$ Group 的 75.6，\textcolor{red}{\textbf{Group 比 CoT 提升 1.8 个百分点}}。
				\end{itemize}
			}
		\end{column}
	\end{columns}

	\vspace{0.4em}

	% 下栏：结论
	\large
	多智能体协作可突破 "单智能体开发" 的能力上限，显著提升编码输出质量。
\end{frame}

\begin{frame}{RQ3: AgentVerse框架在需要多种工具交互的复杂任务中，性能是否优于独立的ReAct智能体?}


\end{frame}

\begin{frame}{RQ4: 在具身场景下，AgentVerse框架下的多智能体协作是否会涌现特定社会行为?}


\end{frame}






