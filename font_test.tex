% 字体测试文档
\documentclass{beamer}

% 基础设置
\usepackage[UTF8]{ctex}
\usepackage[T1]{fontenc}
\usepackage{fontspec}
\usepackage{microtype}

% 字体设置 - 仅西文（英文+数字）使用新罗马字体
\setmainfont{Times New Roman}[
    BoldFont = Times New Roman Bold,
    ItalicFont = Times New Roman Italic,
    BoldItalicFont = Times New Roman Bold Italic,
    Numbers = Lining
]

% 数学字体保持默认
\usepackage{amsmath,amssymb}

% Beamer字体设置
\usefonttheme{professionalfonts}

\title{Font Test - 字体测试}
\author{Test Author}
\date{\today}

\begin{document}

\begin{frame}
    \titlepage
\end{frame}

\begin{frame}{字体测试 Font Test}
    \begin{itemize}
        \item 英文文本 English Text: The quick brown fox jumps over the lazy dog.
        \item 数字测试 Numbers: 1234567890
        \item \textbf{粗体测试 Bold}: This is bold text in Times New Roman
        \item \textit{斜体测试 Italic}: This is italic text in Times New Roman
        \item 数学公式 Math: $E = mc^2$, $\sum_{i=1}^{n} x_i = \int_0^1 f(x)dx$
        \item 代码字体 Code: \texttt{printf("Hello World");}
    \end{itemize}
    
    \vspace{1em}
    
    Mixed text: 这是中文 This is English 123456 $\alpha + \beta = \gamma$
\end{frame}

\end{document}
